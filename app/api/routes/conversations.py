from logging import Logger

from fastapi import APIRouter, Depends, Path, Query, status
from fastapi.responses import StreamingResponse

from app.auth import get_creator_authorize, get_user_id
from app.constant import AgentCode
from app.schema import ConversationCreateSchema, ConversationSchema, MessageSchema, SendMessageRequest
from app.schema.message import MessageContentUpdateRequest
from app.service import ConversationService
from app.service.agno import AgnoService
from app.service.message import MessageService
from pkg.base import Pageable, Pagination
from pkg.base.pageable import SortOrder
from pkg.response import HttpResponse

logger = Logger(__name__)

conversations_router = APIRouter(prefix="/conversations", tags=["Conversation"])


@conversations_router.get(
    "", status_code=status.HTTP_200_OK, response_model=HttpResponse.PaginatedResponse[ConversationSchema]
)
async def get_conversation(
    project_id: str = Query(..., alias="projectId", description="Project ID"),
    limit: int = Query(20, description="Limit"),
    offset: int = Query(0, description="Offset"),
    sort_by: str = Query("created_at", description="Field to sort by"),
    sort_order: SortOrder = Query(SortOrder.desc, description="Sort order (asc or desc)"),
    user_id=Depends(get_user_id),
    conversation_service: ConversationService = Depends(ConversationService),
):
    pageable = Pageable(limit, offset, sort_by, sort_order)
    conversations, count = await conversation_service.get_conversations_by_project(project_id, user_id, pageable)

    return Pagination(
        [ConversationSchema(**conversation.serialize()) for conversation in conversations], count, limit, offset
    ).to_response()


@conversations_router.get(
    "/{conversation_id}", status_code=status.HTTP_200_OK, response_model=HttpResponse.Response[ConversationSchema]
)
async def get_conversation_by_id(
    conversation_id: str,
    creator_authorize=Depends(get_creator_authorize),
    conversation_service: ConversationService = Depends(ConversationService),
):
    conversation = await conversation_service.get_by_id(conversation_id)
    creator_authorize(conversation)
    return ConversationSchema(**conversation.serialize()).to_response()


@conversations_router.post(
    "", status_code=status.HTTP_201_CREATED, response_model=HttpResponse.Response[ConversationSchema]
)
async def create_conversation(
    payload: ConversationCreateSchema,
    user_id=Depends(get_user_id),
    conversation_service: ConversationService = Depends(ConversationService),
):
    conversation = await conversation_service.create(payload.project_id, user_id)
    return ConversationSchema(**conversation.serialize()).to_response(status_code=status.HTTP_201_CREATED)


@conversations_router.delete("/{conversation_id}", status_code=status.HTTP_200_OK, response_model=HttpResponse.Response)
async def delete_conversation(
    conversation_id: str,
    creator_authorize=Depends(get_creator_authorize),
    conversation_service: ConversationService = Depends(ConversationService),
):
    conversation = await conversation_service.get_by_id(conversation_id)
    creator_authorize(conversation)
    await conversation_service.delete_by_id(conversation_id)
    return HttpResponse.ok()


@conversations_router.get(
    "/{conversation_id}/messages",
    status_code=status.HTTP_200_OK,
    response_model=HttpResponse.PaginatedResponse[MessageSchema],
)
async def get_message_by_conversation(
    conversation_id: str,
    limit: int = Query(20, description="Limit"),
    offset: int = Query(0, description="Offset"),
    creator_authorize=Depends(get_creator_authorize),
    conversation_service: ConversationService = Depends(ConversationService),
    message_service: MessageService = Depends(MessageService),
):
    pageable = Pageable(limit, offset)
    conversation = await conversation_service.get_by_id(conversation_id)
    creator_authorize(conversation)
    messages, total = await message_service.get_conversation_messages(conversation_id, pageable)
    return Pagination(
        [MessageSchema(**message.serialize()) for message in messages], total, limit, offset
    ).to_response()


@conversations_router.patch(
    "/{conversation_id}/messages/{message_id}/content",
    response_model=HttpResponse.Response[MessageContentUpdateRequest],
)
async def update_message_content(
    conversation_id: str,
    message_id: str,
    payload: MessageContentUpdateRequest,
    creator_authorize=Depends(get_creator_authorize),
    conversation_service: ConversationService = Depends(ConversationService),
    message_service: MessageService = Depends(MessageService),
):
    # Authorize user access to conversation
    conversation = await conversation_service.get_by_id(conversation_id)
    creator_authorize(conversation)

    # Delegate all business logic to service layer
    updated_message = await message_service.update_message_content_with_session_sync(
        message_id=message_id, conversation_id=conversation_id, content=payload.content
    )

    return MessageSchema(**updated_message.serialize()).to_response()


@conversations_router.post("/{conversation_id}/messages", status_code=status.HTTP_201_CREATED)
async def send_message(
    payload: SendMessageRequest,
    conversation_id: str = Path(..., description="Conversation ID"),
    user_id: str = Depends(get_user_id),
    creator_authorize=Depends(get_creator_authorize),
    conversation_service: ConversationService = Depends(ConversationService),
    message_service: MessageService = Depends(MessageService),
    agno_service: AgnoService = Depends(AgnoService),
):
    conversation = await conversation_service.get_by_id(conversation_id)
    creator_authorize(conversation)

    agent = await agno_service.get_agent(
        agent_code=AgentCode(payload.agent_code),
        conversation_id=conversation_id,
        user_id=user_id,
        references=payload.references,
        mode=payload.mode,
    )

    if payload.sse:
        return StreamingResponse(
            message_service.send_message_see(agent, conversation_id, user_id, payload),
            status_code=status.HTTP_201_CREATED,
            media_type="text/event-stream",
        )

    message = await message_service.send_message(agent, conversation_id, user_id, payload)
    return MessageSchema(**message.serialize()).to_response(status_code=status.HTTP_201_CREATED)
