from typing import Any, Dict, List, Optional

from app.constant import Agent<PERSON><PERSON>, AgentMode
from app.schema import AgentSchema

from .prompts import (
    MASTER_AGENT_DESCRIPTION,
    MASTER_AGENT_ROLE,
    MASTER_AGENT_COORDINATE_INSTRUCTION,
)


class MasterAgent:
    """Interface layer for master agents that delegates to mode-specific implementations."""

    def __init__(
        self,
        agent_id=None,
        user_id=None,
        session_id=None,
        debug_mode: bool = True,
        all_agents_config: List[AgentSchema] = None,
        file_ids: Optional[List[str]] = None,
    ):
        self.agent_id = agent_id
        self.user_id = user_id
        self.session_id = session_id
        self.debug_mode = debug_mode
        self.all_agents_config = all_agents_config or []
        self.file_ids = file_ids

        # Process and organize agent configs for easy access
        self.agents_by_code = self._organize_agents_by_code()

        # Processed master config for easy access
        self.master_config = self._process_master_config()

        # Delegate to the appropriate mode-specific agent
        self._mode_agent = self._create_mode_agent()

    def _safe_get(self, obj, attr: str, default=None):
        """Safely get attribute from object with fallback"""
        try:
            return getattr(obj, attr, default) if obj else default
        except AttributeError:
            return default

    def _safe_get_nested(self, obj, path: str, default=None):
        """Safely get nested attribute using dot notation (e.g., 'prompt_template.instructions')"""
        try:
            result = obj
            for attr in path.split("."):
                result = getattr(result, attr, None)
                if result is None:
                    return default
            return result if result is not None else default
        except AttributeError:
            return default

    def _organize_agents_by_code(self) -> Dict[str, Any]:
        """Organize agent configs by their code for easy lookup"""
        agents_dict = {}

        for agent_config in self.all_agents_config:
            try:
                agent_code = self._safe_get(agent_config, "code", "")
                if agent_code:
                    agents_dict[agent_code] = agent_config
                    agent_name = self._safe_get(agent_config, "name", "Unknown")
                    print(f"Registered agent: {agent_code} - {agent_name}")
            except Exception as e:
                print(f"Error processing agent config: {e}")
                continue

        return agents_dict

    def _process_master_config(self) -> Dict[str, Any]:
        """Process master config and extract commonly used values"""
        master_config = self.agents_by_code.get(AgentCode.Master)

        if not master_config:
            return {
                "model_id": "gpt-4o",
                "system_prompt": MASTER_AGENT_COORDINATE_INSTRUCTION,
                "additional_prompt": "",
                "name": "Master Agent",
                "role": MASTER_AGENT_ROLE,
                "description": MASTER_AGENT_DESCRIPTION,
                "mode": AgentMode.COORDINATE,
            }

        return {
            "model_id": self._safe_get(master_config, "model_id", "gpt-4o"),
            "system_prompt": self._safe_get(
                master_config, "system_prompt",
            ),
            "additional_prompt": self._safe_get(master_config, "additional_prompt", ""),
            "name": self._safe_get(master_config, "name", "Master Agent"),
            "role": self._safe_get(master_config, "role", "Coordinator"),
            "tools": self._safe_get(master_config, "tools", []),
            "description": self._safe_get(master_config, "description", ""),
            "mode": self._safe_get(master_config, "mode", AgentMode.COORDINATE),
        }

    def _create_mode_agent(self):
        """Create the appropriate mode-specific agent based on configuration."""
        mode = self.master_config["mode"]

        if mode == AgentMode.ROUTE:
            from .route_agent import RouteAgent
            return RouteAgent(
                agent_id=self.agent_id,
                user_id=self.user_id,
                session_id=self.session_id,
                debug_mode=self.debug_mode,
                all_agents_config=self.all_agents_config,
                file_ids=self.file_ids,
                master_config=self.master_config,
                agents_by_code=self.agents_by_code,
            )
        elif mode == AgentMode.COORDINATE:
            from .coordinate_agent import CoordinateAgent
            return CoordinateAgent(
                agent_id=self.agent_id,
                user_id=self.user_id,
                session_id=self.session_id,
                debug_mode=self.debug_mode,
                all_agents_config=self.all_agents_config,
                file_ids=self.file_ids,
                master_config=self.master_config,
                agents_by_code=self.agents_by_code,
            )
        else:
            raise ValueError(f"Unsupported agent mode: {mode}")

    @property
    def agent(self):
        """Delegate to the mode-specific agent implementation."""
        return self._mode_agent.agent
