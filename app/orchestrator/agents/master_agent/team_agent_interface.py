from typing import Any, Dict, List, Optional

from agno.team.team import Team

from app.constant import AgentMode


class TeamAgentFactory:
    """Factory class to create appropriate Team implementations based on mode."""

    @staticmethod
    def create_team_agent(
        mode: AgentMode,
        team_id: str = None,
        user_id: str = None,
        session_id: str = None,
        debug_mode: bool = True,
        master_config: Dict[str, Any] = None,
        file_ids: Optional[List[str]] = None,
        ur_agent=None,
        hlr_agent=None,
        usecase_agent=None,
        screen_agent=None,
        storage=None,
        knowledge_base=None,
    ) -> Team:
        """Create and return the appropriate Team implementation based on mode."""

        if mode == AgentMode.ROUTE:
            from .route_team_agent import RouteTeamAgent
            return RouteTeamAgent(
                team_id=team_id,
                user_id=user_id,
                session_id=session_id,
                debug_mode=debug_mode,
                master_config=master_config,
                file_ids=file_ids,
                ur_agent=ur_agent,
                hlr_agent=hlr_agent,
                usecase_agent=usecase_agent,
                screen_agent=screen_agent,
                storage=storage,
                knowledge_base=knowledge_base,
            )
        elif mode == AgentMode.COORDINATE:
            from .coordinate_team_agent import CoordinateTeamAgent
            return CoordinateTeamAgent(
                team_id=team_id,
                user_id=user_id,
                session_id=session_id,
                debug_mode=debug_mode,
                master_config=master_config,
                file_ids=file_ids,
                ur_agent=ur_agent,
                hlr_agent=hlr_agent,
                usecase_agent=usecase_agent,
                screen_agent=screen_agent,
                storage=storage,
                knowledge_base=knowledge_base,
            )
        else:
            raise ValueError(f"Unsupported agent mode: {mode}")
