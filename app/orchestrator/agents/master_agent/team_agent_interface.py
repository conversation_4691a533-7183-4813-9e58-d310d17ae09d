from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional

from agno.team.team import Team

from app.constant import AgentMode


class TeamAgent(ABC):
    """Abstract base class for team agent implementations."""
    
    def __init__(
        self,
        agent_id: str = None,
        user_id: str = None,
        session_id: str = None,
        debug_mode: bool = True,
        master_config: Dict[str, Any] = None,
        file_ids: Optional[List[str]] = None,
        ur_agent=None,
        hlr_agent=None,
        usecase_agent=None,
        screen_agent=None,
        storage=None,
        knowledge_base=None,
    ):
        self.agent_id = agent_id
        self.user_id = user_id
        self.session_id = session_id
        self.debug_mode = debug_mode
        self.master_config = master_config or {}
        self.file_ids = file_ids
        self.ur_agent = ur_agent
        self.hlr_agent = hlr_agent
        self.usecase_agent = usecase_agent
        self.screen_agent = screen_agent
        self.storage = storage
        self.knowledge_base = knowledge_base
        self._team_agent = None

    @abstractmethod
    def get_mode(self) -> AgentMode:
        """Return the agent mode for this implementation."""
        pass

    @abstractmethod
    def get_instructions(self) -> str:
        """Return the specific instructions for this agent mode."""
        pass

    @abstractmethod
    def get_model_config(self) -> Dict[str, Any]:
        """Return the model configuration for this agent mode."""
        pass

    @abstractmethod
    def get_additional_params(self) -> Dict[str, Any]:
        """Return any additional parameters specific to this agent mode."""
        pass

    def create_team_agent(self) -> Team:
        """Create and return the team agent instance."""
        if not self._team_agent:
            # Base team parameters common to all modes
            team_params = {
                "team_id": self.agent_id,
                "name": self.master_config.get("name", "Master Agent"),
                "description": self.master_config.get("description", ""),
                "members": [self.ur_agent, self.hlr_agent, self.usecase_agent, self.screen_agent],
                "instructions": self.get_instructions(),
                "markdown": True,
                "show_members_responses": True,
                "share_member_interactions": True,
                "enable_agentic_context": True,
                "add_datetime_to_instructions": True,
                "debug_mode": self.debug_mode,
                "user_id": self.user_id,
                "session_id": self.session_id,
                "storage": self.storage,
                "add_history_to_messages": True,
                "num_history_runs": 5,
                "mode": self.get_mode(),
                "knowledge": self.knowledge_base,
                "search_knowledge": True,
            }

            # Add model configuration
            team_params.update(self.get_model_config())

            # Add mode-specific additional parameters
            team_params.update(self.get_additional_params())

            # Add knowledge parameters if file_ids is provided
            if self.file_ids:
                team_params.update({"knowledge_filters": {"document_id": self.file_ids}})

            self._team_agent = Team(**team_params)
        
        return self._team_agent

    @property
    def agent(self) -> Team:
        """Property to get the team agent instance."""
        return self.create_team_agent()


class TeamAgentFactory:
    """Factory class to create appropriate TeamAgent implementations based on mode."""
    
    @staticmethod
    def create_team_agent(
        mode: AgentMode,
        agent_id: str = None,
        user_id: str = None,
        session_id: str = None,
        debug_mode: bool = True,
        master_config: Dict[str, Any] = None,
        file_ids: Optional[List[str]] = None,
        ur_agent=None,
        hlr_agent=None,
        usecase_agent=None,
        screen_agent=None,
        storage=None,
        knowledge_base=None,
    ) -> TeamAgent:
        """Create and return the appropriate TeamAgent implementation based on mode."""
        
        if mode == AgentMode.ROUTE:
            from .route_team_agent import RouteTeamAgent
            return RouteTeamAgent(
                agent_id=agent_id,
                user_id=user_id,
                session_id=session_id,
                debug_mode=debug_mode,
                master_config=master_config,
                file_ids=file_ids,
                ur_agent=ur_agent,
                hlr_agent=hlr_agent,
                usecase_agent=usecase_agent,
                screen_agent=screen_agent,
                storage=storage,
                knowledge_base=knowledge_base,
            )
        elif mode == AgentMode.COORDINATE:
            from .coordinate_team_agent import CoordinateTeamAgent
            return CoordinateTeamAgent(
                agent_id=agent_id,
                user_id=user_id,
                session_id=session_id,
                debug_mode=debug_mode,
                master_config=master_config,
                file_ids=file_ids,
                ur_agent=ur_agent,
                hlr_agent=hlr_agent,
                usecase_agent=usecase_agent,
                screen_agent=screen_agent,
                storage=storage,
                knowledge_base=knowledge_base,
            )
        else:
            raise ValueError(f"Unsupported agent mode: {mode}")
