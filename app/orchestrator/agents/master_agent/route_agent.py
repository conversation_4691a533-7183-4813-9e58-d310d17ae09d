from typing import Any, Dict, List, Optional

from agno.storage.mongodb import MongoDbStorage
from agno.team.team import Team

from app.config.get_config import get_config
from app.constant import AgentCode, AgentMode
from app.factories.chat_factory import create_chat_model
from app.factories.knowledge_base_factory import create_knowledge_base
from app.model import AgnoSession
from app.orchestrator.agents.hlr_agent.agent import HLRAgent
from app.orchestrator.agents.ur_agent.agent import URAgent
from app.orchestrator.agents.usecase_agent.agent import UseCaseAgent
from app.orchestrator.agents.screen_agent.agent import ScreenAgent
from app.schema import AgentSchema
from app.utils.config_helper import <PERSON><PERSON><PERSON><PERSON>gH<PERSON><PERSON>

from .prompts import MASTER_AGENT_ROUTE_INSTRUCTION


class RouteAgent:
    """Route mode agent implementation that handles all route-specific behavior and configuration."""
    
    def __init__(
        self,
        agent_id=None,
        user_id=None,
        session_id=None,
        debug_mode: bool = True,
        all_agents_config: List[AgentSchema] = None,
        file_ids: Optional[List[str]] = None,
        master_config: Dict[str, Any] = None,
        agents_by_code: Dict[str, Any] = None,
    ):
        self._team_agent = None
        self.agent_id = agent_id
        self.user_id = user_id
        self.session_id = session_id
        self.debug_mode = debug_mode
        self.all_agents_config = all_agents_config or []
        self.file_ids = file_ids
        self.master_config = master_config or {}
        self.agents_by_code = agents_by_code or {}

    @property
    def ur_agent(self):
        ur_agent_config = self.agents_by_code.get(AgentCode.UserRequirement)
        return URAgent(
            agent_id=AgentCode.UserRequirement,
            user_id=self.user_id,
            session_id=self.session_id,
            debug_mode=self.debug_mode,
            agent_config=ur_agent_config,
            file_ids=self.file_ids,
        ).agent

    @property
    def hlr_agent(self):
        hlr_agent_config = self.agents_by_code.get(AgentCode.HighLevelRequirement)
        return HLRAgent(
            agent_id=AgentCode.HighLevelRequirement,
            user_id=self.user_id,
            session_id=self.session_id,
            debug_mode=self.debug_mode,
            agent_config=hlr_agent_config,
            file_ids=self.file_ids,
        ).agent

    @property
    def usecase_agent(self):
        usecase_agent_config = self.agents_by_code.get(AgentCode.UseCase)
        return UseCaseAgent(
            agent_id=AgentCode.UseCase,
            user_id=self.user_id,
            session_id=self.session_id,
            debug_mode=self.debug_mode,
            agent_config=usecase_agent_config,
            file_ids=self.file_ids,
        ).agent
    
    @property
    def screen_agent(self):
        screen_agent_config = self.agents_by_code.get(AgentCode.Screen)
        return ScreenAgent(
            agent_id=AgentCode.Screen,
            user_id=self.user_id,
            session_id=self.session_id,
            debug_mode=self.debug_mode,
            agent_config=screen_agent_config,
            file_ids=self.file_ids,
        ).agent

    def _get_instructions(self) -> str:
        """Get route-specific instructions."""
        system_prompt = self.master_config.get("system_prompt", MASTER_AGENT_ROUTE_INSTRUCTION)
        additional_prompt = self.master_config.get("additional_prompt", "")
        
        return AgentConfigHelper.merge_instructions(system_prompt, additional_prompt)

    def _get_model_config(self) -> Dict[str, Any]:
        """Get route-specific model configuration."""
        config = get_config().chat_model_config
        model_id = self.master_config.get("model_id", "gpt-4o")
        
        return {
            "model": create_chat_model(config, model_id=model_id),
        }

    @property
    def agent(self):
        """Create and return the Team instance for route mode."""
        if not self._team_agent:
            # Create necessary dependencies
            knowledge_base = create_knowledge_base()
            mongo_config = get_config().mongo_config
            storage = MongoDbStorage(
                collection_name=AgnoSession.Settings.name,
                db_name=mongo_config.database_name,
                db_url=mongo_config.url,
            )
            
            print("Additional prompt", self.master_config["additional_prompt"])

            # Route-specific team parameters
            team_params = {
                "team_id": AgentCode.Master,
                "name": self.master_config.get("name", "Master Agent"),
                "description": self.master_config.get("description", ""),
                "members": [self.ur_agent, self.hlr_agent, self.usecase_agent, self.screen_agent],
                "instructions": self._get_instructions(),
                "markdown": True,
                "show_members_responses": True,
                "share_member_interactions": True,
                "enable_agentic_context": True,
                "add_datetime_to_instructions": True,
                "debug_mode": self.debug_mode,
                "user_id": self.user_id,
                "session_id": self.session_id,
                "storage": storage,
                "add_history_to_messages": True,
                "num_history_runs": 5,
                "mode": AgentMode.ROUTE,
                "knowledge": knowledge_base,
                "search_knowledge": True,
            }

            # Add route-specific model configuration
            team_params.update(self._get_model_config())

            # Add knowledge parameters if file_ids is provided
            if self.file_ids:
                team_params.update({"knowledge_filters": {"document_id": self.file_ids}})

            # Create Team instance
            self._team_agent = Team(**team_params)

        return self._team_agent
