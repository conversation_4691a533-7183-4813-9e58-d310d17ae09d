from textwrap import dedent
from typing import Any, Dict, List, Optional

from agno.team.team import Team

from app.config.get_config import get_config
from app.constant import AgentMode
from app.factories.chat_factory import create_chat_model
from app.utils.config_helper import AgentConfigHelper

from .prompts import MASTER_AGENT_ROUTE_INSTRUCTION


class RouteTeamAgent(Team):
    """Team agent implementation for route mode that inherits from Team."""

    def __init__(
        self,
        team_id: str = None,
        user_id: str = None,
        session_id: str = None,
        debug_mode: bool = True,
        master_config: Dict[str, Any] = None,
        file_ids: Optional[List[str]] = None,
        ur_agent=None,
        hlr_agent=None,
        usecase_agent=None,
        screen_agent=None,
        storage=None,
        knowledge_base=None,
        **kwargs
    ):
        self.master_config = master_config or {}
        self.file_ids = file_ids

        # Get configuration
        config = get_config().chat_model_config
        model_id = self.master_config.get("model_id", "gpt-4o")

        # Prepare instructions
        system_prompt = self.master_config.get("system_prompt", MASTER_AGENT_ROUTE_INSTRUCTION)
        additional_prompt = self.master_config.get("additional_prompt", "")
        instructions = dedent(
            AgentConfigHelper.merge_instructions(system_prompt, additional_prompt)
        )

        # Base team parameters for route mode
        team_params = {
            "team_id": team_id,
            "name": self.master_config.get("name", "Master Agent"),
            "description": self.master_config.get("description", ""),
            "members": [ur_agent, hlr_agent, usecase_agent, screen_agent],
            "instructions": instructions,
            "model": create_chat_model(config, model_id=model_id),
            "markdown": True,
            "show_members_responses": True,
            "share_member_interactions": True,
            "enable_agentic_context": True,
            "add_datetime_to_instructions": True,
            "debug_mode": debug_mode,
            "user_id": user_id,
            "session_id": session_id,
            "storage": storage,
            "add_history_to_messages": True,
            "num_history_runs": 5,
            "mode": AgentMode.ROUTE,
            "knowledge": knowledge_base,
            "search_knowledge": True,
        }

        # Add knowledge parameters if file_ids is provided
        if self.file_ids:
            team_params.update({"knowledge_filters": {"document_id": self.file_ids}})

        # Update with any additional kwargs
        team_params.update(kwargs)

        # Initialize the Team parent class
        super().__init__(**team_params)
