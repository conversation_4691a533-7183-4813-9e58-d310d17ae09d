from textwrap import dedent
from typing import Any, Dict

from app.config.get_config import get_config
from app.constant import Agent<PERSON><PERSON>
from app.factories.chat_factory import create_chat_model
from app.utils.config_helper import <PERSON>ConfigHelper

from .prompts import MASTER_AGENT_ROUTE_INSTRUCTION
from .team_agent_interface import TeamAgentConfigurator


class RouteTeamConfigurator(TeamAgentConfigurator):
    """Team configurator for route mode using composition pattern."""

    def get_mode(self) -> AgentMode:
        """Return the agent mode for this configurator."""
        return AgentMode.ROUTE

    def get_instructions(self) -> str:
        """Return the specific instructions for route mode."""
        system_prompt = self.master_config.get("system_prompt", MASTER_AGENT_ROUTE_INSTRUCTION)
        additional_prompt = self.master_config.get("additional_prompt", "")

        return dedent(
            AgentConfigHelper.merge_instructions(system_prompt, additional_prompt)
        )

    def get_model_config(self) -> Dict[str, Any]:
        """Return the model configuration for route mode."""
        config = get_config().chat_model_config
        model_id = self.master_config.get("model_id", "gpt-4o")

        return {
            "model": create_chat_model(config, model_id=model_id),
        }

    def get_additional_params(self) -> Dict[str, Any]:
        """Return any additional parameters specific to route mode."""
        # Route mode doesn't need additional tools like reasoning
        return {}
