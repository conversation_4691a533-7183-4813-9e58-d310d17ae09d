from textwrap import dedent
from typing import Any, Dict

from agno.tools.reasoning import ReasoningTools

from app.config.get_config import get_config
from app.constant import AgentMode
from app.factories.chat_factory import create_chat_model
from app.utils.config_helper import <PERSON><PERSON>onfigHel<PERSON>

from .prompts import MASTER_AGENT_COORDINATE_INSTRUCTION
from .team_agent_interface import TeamAgentConfigurator


class CoordinateTeamConfigurator(TeamAgentConfigurator):
    """Team configurator for coordinate mode using composition pattern."""

    def get_mode(self) -> AgentMode:
        """Return the agent mode for this configurator."""
        return AgentMode.COORDINATE

    def get_instructions(self) -> str:
        """Return the specific instructions for coordinate mode."""
        system_prompt = self.master_config.get("system_prompt", MASTER_AGENT_COORDINATE_INSTRUCTION)
        additional_prompt = self.master_config.get("additional_prompt", "")

        return dedent(
            AgentConfigHelper.merge_instructions(system_prompt, additional_prompt)
        )

    def get_model_config(self) -> Dict[str, Any]:
        """Return the model configuration for coordinate mode."""
        config = get_config().chat_model_config
        model_id = self.master_config.get("model_id", "gpt-o4-mini")

        return {
            "model": create_chat_model(config, model_id=model_id),
        }

    def get_additional_params(self) -> Dict[str, Any]:
        """Return any additional parameters specific to coordinate mode."""
        # Coordinate mode includes reasoning tools
        return {
            "tools": [ReasoningTools(add_instructions=True)],
        }
