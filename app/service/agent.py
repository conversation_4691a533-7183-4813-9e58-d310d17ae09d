from typing import Optional

from app.model import Agent
from app.constant import Agent<PERSON><PERSON>, AgentType
from pkg.exception import HttpException
from pkg.message import ErrorMessage
from beanie.operators import Eq, Or


class AgentService:
    def __init__(self):
        pass

    @staticmethod
    async def find_all_by_project(project: Optional[str]):
        system_agents = await Agent.find(Eq(Agent.project, None), Eq(Agent.is_active, True)).to_list()
        project_agents = await Agent.find(Eq(Agent.project, project), Eq(Agent.is_active, True)).to_list()
        existed_project_agents = map(lambda x: x.code, project_agents)
        project_agents.extend(filter(lambda x: x.code not in existed_project_agents, system_agents))
        return project_agents

    @staticmethod
    async def find_by_code(agent_code: str, version: str = None, project: Optional[str] = None, mode: Optional[AgentMode] = None):
        if version:
            query_conditions = [Agent.code == agent_code, Agent.version == version,
                               Or(Eq(Agent.project == project, Eq(Agent.project, None)))]
            if mode is not None:
                query_conditions.append(Agent.mode == mode)

            agent = await Agent.find_one(*query_conditions)
            if not agent:
                raise HttpException.not_found(ErrorMessage.AGENT_NOT_EXISTS,
                                              {Agent.code: agent_code, Agent.version: version})
            return agent

        query_conditions = [Agent.code == agent_code, Agent.is_active == True,
                           Agent.project == project or Agent.project is None]
        if mode is not None:
            query_conditions.append(Agent.mode == mode)

        agent = await Agent.find_one(*query_conditions)
        if not agent:
            raise HttpException.not_found(ErrorMessage.AGENT_NOT_EXISTS, {Agent.code: agent_code})
        return agent

    @staticmethod
    def __next_version(version: str, major_version: bool = False) -> str:
        try:
            major, minor = version.split('.')
            if major_version:
                major = int(major) + 1
                minor = 0
            else:
                minor = int(minor) + 1
            return f"{major}.{minor}"
        except (ValueError, IndexError):
            return "0.1"

    @staticmethod
    def __version_comparable_key(agent: Agent):
        version = agent.version or "0.1"
        try:
            major, minor = version.split('.')
            return int(major), int(minor)
        except (ValueError, IndexError):
            return 0, 1

    async def update_by_code(self, agent_code: str, update_payload: dict):
        project = update_payload.get("project", None)
        versions = await Agent.find(Agent.code == agent_code,
                                    Agent.project == project).to_list()

        latest_version = max(versions, key=self.__version_comparable_key)
        if not latest_version:
            latest_version = self.find_by_code(agent_code)
            latest_version.version = "0.0"

        lastest_version_dump = latest_version.model_dump()
        del lastest_version_dump["id"]
        next_version = Agent(**lastest_version_dump)

        next_version.system_prompt = update_payload["system_prompt"]
        next_version.additional_prompt = update_payload["additional_prompt"]
        next_version.model_id = update_payload["model_id"]
        next_version.project = project

        next_version.version = self.__next_version(latest_version.version)
        next_version.is_active = True
        await Agent.save(next_version)

        await Agent.find(Agent.code == agent_code, Agent.project == update_payload.get("project", None),
                         Agent.id != next_version.id).update({
            "$set": {Agent.is_active: False}
        })

        return next_version

    @staticmethod
    async def find_all_versions_by_code(self, agent_code: str, project: Optional[str] = None):
        agents = await Agent.find(Agent.code == agent_code, Agent.project == project).to_list()

        if not agents:
            raise HttpException.not_found(ErrorMessage.AGENT_NOT_EXISTS, {"code": agent_code})

        return sorted(agents, key=self.__version_comparable_key, reverse=True)
