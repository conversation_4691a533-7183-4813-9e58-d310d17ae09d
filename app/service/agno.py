from logging import Logger
from typing import Any, Dict, List, Optional

from fastapi import Depends
from pydantic import BaseModel

from app.constant import AgentM<PERSON>, ReferenceType
from app.factories.agent_factory import Agent<PERSON>ode, AgentFactory
from app.model import AgnoSession
from app.schema.message import ReferenceSchema

logger = Logger(__name__)


class AgnoService:
    def __init__(self, agent_factory: AgentFactory = Depends(AgentFactory)):
        self.agent_factory = agent_factory

    async def update_session_message_content(
        self, conversation_id: str, message_id: str, content: str, run_id: str = None
    ) -> None:
        """
        Update message content in the session state.

        Args:
            conversation_id: ID of the conversation (used as session_id)
            message_id: ID of the message to update
            content: New content for the message
            run_id: Run ID to find specific run (optional)
        """
        session = await AgnoSession.find_one(AgnoSession.session_id == conversation_id)
        if session:
            # Access memory.runs to find the run containing messages
            memory = getattr(session, "memory", None)
            if memory and isinstance(memory, dict) and "runs" in memory:
                runs = memory["runs"]

                # Find the run that contains the message we want to update
                target_run = None

                # If we have run_id, try to find matching run (from end to begin)
                if run_id:
                    for i in range(len(runs) - 1, -1, -1):
                        run = runs[i]
                        if isinstance(run, dict) and run.get("run_id") == str(run_id):
                            target_run = run
                            break

                # If no matching run found, use the last run
                if not target_run and runs:
                    target_run = runs[-1]

                if target_run and isinstance(target_run, dict) and "messages" in target_run:
                    messages = target_run["messages"]

                    # Find the last AI/assistant message in this run
                    for i in range(len(messages) - 1, -1, -1):
                        session_message = messages[i]
                        if isinstance(session_message, dict) and session_message.get("role") == "assistant":
                            # Update the content
                            session_message["content"] = content
                            break

                    # Save updated session back to database
                    await session.save()

    async def delete_session(self, session_id: str):
        await AgnoSession.find_one(AgnoSession.session_id == session_id).delete()

    async def get_agent(
        self,
        agent_code: AgentCode,
        user_id: str,
        conversation_id: str,
        debug_mode: bool = False,
        references: Optional[List[ReferenceSchema]] = None,
        mode: Optional[AgentMode] = None,
    ):
        # Extract file IDs from references that have type File or LegacyFile
        file_ids = []
        if references:
            file_ids = [ref.id for ref in references if ref.type in [ReferenceType.File, ReferenceType.LegacyFile]]

        return await self.agent_factory.create_agent(
            agent_code=agent_code,
            user_id=user_id,
            session_id=conversation_id,
            debug_mode=debug_mode,
            file_ids=file_ids if file_ids else None,
            mode=mode,
        )

    async def get_session_data(self, session_id: str):
        class SessionDataView(BaseModel):
            session_data: Optional[Dict[str, Any]]

        session = await AgnoSession.find_one(AgnoSession.session_id == session_id).project(SessionDataView)
        return session.session_data if session else None
